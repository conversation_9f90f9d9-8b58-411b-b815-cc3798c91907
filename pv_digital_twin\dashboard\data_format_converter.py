"""
数据格式转换模块

提供pvlib结果与Django API格式之间的转换功能。
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Union


class DataFormatConverter:
    """数据格式转换器"""
    
    @staticmethod
    def pvlib_to_django_format(pvlib_results: Union[pd.DataFrame, Dict]) -> Dict[str, List]:
        """
        将pvlib结果转换为Django API格式
        
        Args:
            pvlib_results: pvlib仿真结果，可以是DataFrame或字典
            
        Returns:
            dict: Django API格式的数据字典
        """
        try:
            # 如果输入是字典，转换为DataFrame
            if isinstance(pvlib_results, dict):
                if 'datetime' in pvlib_results:
                    # 单个时间点的数据
                    df = pd.DataFrame([pvlib_results])
                    if 'datetime' in df.columns:
                        df['datetime'] = pd.to_datetime(df['datetime'])
                else:
                    # 多个时间点的数据
                    df = pd.DataFrame(pvlib_results)
            else:
                df = pvlib_results.copy()
            
            # 确保有datetime列或索引
            if 'datetime' not in df.columns and not isinstance(df.index, pd.DatetimeIndex):
                # 如果没有时间信息，创建默认时间序列
                df.index = pd.date_range(
                    start=datetime.now(), 
                    periods=len(df), 
                    freq='H'
                )
            
            # 如果datetime在列中，设置为索引
            if 'datetime' in df.columns:
                df = df.set_index('datetime')
            
            # 确保索引是DatetimeIndex
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)
            
            # 生成时间戳列表
            timestamps = df.index.strftime("%Y-%m-%d %H:%M:%S").tolist()
            
            # 转换各个数据列，使用安全的获取方式
            result = {
                "timestamps": timestamps,
                "ac_power": DataFormatConverter._safe_get_column(df, ['ac_power', 'p_ac', 'AC_POWER'], 0).tolist(),
                "dc_power": DataFormatConverter._safe_get_column(df, ['dc_power', 'p_dc', 'p_mp', 'DC_POWER'], 0).tolist(),
                "temp_air": DataFormatConverter._safe_get_column(df, ['temp_air', 'temperature_air', 'TEMP_AIR'], 25).tolist(),
                "temp_cell": DataFormatConverter._safe_get_column(df, ['temp_cell', 'cell_temperature', 'TEMP_CELL'], 25).tolist(),
                "ghi": DataFormatConverter._safe_get_column(df, ['ghi', 'global_horizontal_irradiance', 'GHI'], 0).tolist(),
                "efficiency": DataFormatConverter._calculate_efficiency(df).tolist()
            }
            
            return result
            
        except Exception as e:
            print(f"数据格式转换错误: {e}")
            # 返回默认的空数据结构
            return {
                "timestamps": [],
                "ac_power": [],
                "dc_power": [],
                "temp_air": [],
                "temp_cell": [],
                "ghi": [],
                "efficiency": []
            }
    
    @staticmethod
    def _safe_get_column(df: pd.DataFrame, column_names: List[str], default_value: float) -> pd.Series:
        """
        安全地获取DataFrame中的列，支持多个可能的列名
        
        Args:
            df: DataFrame
            column_names: 可能的列名列表
            default_value: 默认值
            
        Returns:
            pd.Series: 列数据或默认值填充的Series
        """
        for col_name in column_names:
            if col_name in df.columns:
                series = df[col_name].fillna(default_value)
                return pd.to_numeric(series, errors='coerce').fillna(default_value)
        
        # 如果都没找到，返回默认值填充的Series
        return pd.Series([default_value] * len(df), index=df.index)
    
    @staticmethod
    def _calculate_efficiency(df: pd.DataFrame) -> pd.Series:
        """
        计算系统效率，保持与原有算法一致
        
        Args:
            df: 包含功率和辐照度数据的DataFrame
            
        Returns:
            pd.Series: 效率数据
        """
        try:
            # 获取AC功率
            ac_power = DataFormatConverter._safe_get_column(df, ['ac_power', 'p_ac', 'AC_POWER'], 0)
            
            # 获取GHI
            ghi = DataFormatConverter._safe_get_column(df, ['ghi', 'global_horizontal_irradiance', 'GHI'], 0)
            
            # 获取系统容量（假设值，实际应该从系统参数获取）
            # 这里使用一个合理的默认值
            system_capacity_kw = 5.0  # 5kW系统
            
            # 计算效率：(实际功率 / 理论最大功率) * 100
            # 理论最大功率 = (GHI / 1000) * 系统容量
            theoretical_max_power = (ghi / 1000.0) * system_capacity_kw
            
            # 避免除零错误
            efficiency = np.where(
                theoretical_max_power > 0,
                (ac_power / theoretical_max_power) * 100,
                0
            )
            
            # 限制效率在合理范围内 (0-100%)
            efficiency = np.clip(efficiency, 0, 100)
            
            return pd.Series(efficiency, index=df.index)
            
        except Exception as e:
            print(f"效率计算错误: {e}")
            # 返回默认效率值
            return pd.Series([0] * len(df), index=df.index)
    
    @staticmethod
    def django_to_pvlib_weather(django_weather: Union[pd.DataFrame, Dict]) -> pd.DataFrame:
        """
        将Django天气数据转换为pvlib格式
        
        Args:
            django_weather: Django格式的天气数据
            
        Returns:
            pd.DataFrame: pvlib格式的天气数据
        """
        try:
            # 如果输入是字典，转换为DataFrame
            if isinstance(django_weather, dict):
                df = pd.DataFrame([django_weather])
            else:
                df = django_weather.copy()
            
            # 处理时间索引
            if 'datetime' in df.columns:
                df['datetime'] = pd.to_datetime(df['datetime'])
                df = df.set_index('datetime')
            elif not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)
            
            # 确保必要的列存在
            required_columns = {
                'ghi': 0,
                'temp_air': 25,
                'wind_speed': 1,
                'dni': None,  # 将从ghi计算
                'dhi': None   # 将从ghi计算
            }
            
            for col, default_val in required_columns.items():
                if col not in df.columns and default_val is not None:
                    df[col] = default_val
            
            # 如果缺少DNI和DHI，从GHI估算
            if 'dni' not in df.columns or 'dhi' not in df.columns:
                df = DataFormatConverter._estimate_dni_dhi(df)
            
            # 确保数值类型
            numeric_columns = ['ghi', 'temp_air', 'wind_speed', 'dni', 'dhi']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
            
            return df
            
        except Exception as e:
            print(f"天气数据转换错误: {e}")
            # 返回默认天气数据
            return pd.DataFrame({
                'ghi': [0],
                'temp_air': [25],
                'wind_speed': [1],
                'dni': [0],
                'dhi': [0]
            }, index=pd.DatetimeIndex([datetime.now()]))
    
    @staticmethod
    def _estimate_dni_dhi(df: pd.DataFrame) -> pd.DataFrame:
        """
        从GHI估算DNI和DHI
        
        Args:
            df: 包含GHI的DataFrame
            
        Returns:
            pd.DataFrame: 包含DNI和DHI的DataFrame
        """
        try:
            # 简化的估算方法
            # 在实际应用中，应该使用更精确的模型如Erbs模型
            ghi = df['ghi'].fillna(0)
            
            # 简单估算：DNI约为GHI的80%，DHI约为GHI的20%
            df['dni'] = ghi * 0.8
            df['dhi'] = ghi * 0.2
            
            return df
            
        except Exception as e:
            print(f"DNI/DHI估算错误: {e}")
            df['dni'] = 0
            df['dhi'] = 0
            return df
    
    @staticmethod
    def format_system_info(pv_twin_info: Dict) -> Dict:
        """
        格式化系统信息以保持API兼容性
        
        Args:
            pv_twin_info: PVDigitalTwin提供的系统信息
            
        Returns:
            dict: 格式化后的系统信息
        """
        try:
            return {
                "installed_capacity": pv_twin_info.get("installed_capacity", 0),
                "current_power": pv_twin_info.get("current_power", 0),
                "max_power_today": pv_twin_info.get("max_power_today", 0),
                "max_ghi_today": pv_twin_info.get("max_ghi_today", 0),
                "max_efficiency_today": pv_twin_info.get("max_efficiency_today", 0),
                "daily_energy": pv_twin_info.get("daily_energy", 0),
                "current_temp_air": pv_twin_info.get("current_temp_air", 25),
                "current_temp_cell": pv_twin_info.get("current_temp_cell", 25),
            }
        except Exception as e:
            print(f"系统信息格式化错误: {e}")
            return {
                "installed_capacity": 0,
                "current_power": 0,
                "max_power_today": 0,
                "max_ghi_today": 0,
                "max_efficiency_today": 0,
                "daily_energy": 0,
                "current_temp_air": 25,
                "current_temp_cell": 25,
            }
