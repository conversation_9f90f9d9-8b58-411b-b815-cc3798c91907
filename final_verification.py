#!/usr/bin/env python
"""
阶段一重构最终验证脚本
"""

import os
import sys
from pathlib import Path

# 添加Django项目路径
project_root = Path(__file__).resolve().parent
django_path = project_root / 'pv_digital_twin'
sys.path.insert(0, str(django_path))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pv_digital_twin.settings')

import django
django.setup()

def verify_files_created():
    """验证所有必要文件是否已创建"""
    print("📁 验证文件创建...")
    
    required_files = [
        'pv_digital_twin/dashboard/config.py',
        'pv_digital_twin/dashboard/real_pv_adapter.py',
        'pv_digital_twin/dashboard/data_format_converter.py',
        'pv_digital_twin/dashboard/weather_data_bridge.py',
    ]
    
    all_exist = True
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")
            all_exist = False
    
    return all_exist

def verify_imports():
    """验证所有模块可以正常导入"""
    print("\n📦 验证模块导入...")
    
    modules = [
        ('dashboard.config', 'SimulationConfig'),
        ('dashboard.data_format_converter', 'DataFormatConverter'),
        ('dashboard.weather_data_bridge', 'WeatherDataBridge'),
        ('dashboard.real_pv_adapter', 'RealPVModelAdapter'),
        ('dashboard.pv_model_adapter', 'PVModelAdapter'),
    ]
    
    all_imported = True
    for module_name, class_name in modules:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"   ✅ {module_name}.{class_name}")
        except Exception as e:
            print(f"   ❌ {module_name}.{class_name}: {e}")
            all_imported = False
    
    return all_imported

def verify_configuration():
    """验证配置功能"""
    print("\n⚙️ 验证配置功能...")
    
    try:
        from dashboard.config import SimulationConfig, get_adapter_class
        
        # 测试配置读取
        use_real = SimulationConfig.use_real_simulation()
        print(f"   ✅ 配置读取: {use_real}")
        
        # 测试路径获取
        src_path = SimulationConfig.get_src_path()
        print(f"   ✅ src路径: {src_path}")
        
        # 测试适配器类获取
        adapter_class = get_adapter_class()
        print(f"   ✅ 适配器类: {adapter_class.__name__}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 配置功能错误: {e}")
        return False

def verify_switching():
    """验证切换功能"""
    print("\n🔄 验证切换功能...")
    
    try:
        from dashboard.config import SimulationConfig, get_adapter_class
        from dashboard.pv_model_adapter import PVModelAdapter
        
        # 测试默认模式
        os.environ.pop('USE_REAL_SIMULATION', None)
        PVModelAdapter._instance = None
        adapter_class = get_adapter_class()
        print(f"   ✅ 默认模式: {adapter_class.__name__}")
        
        # 测试真实模式
        os.environ['USE_REAL_SIMULATION'] = 'true'
        PVModelAdapter._instance = None
        adapter_class = get_adapter_class()
        print(f"   ✅ 真实模式: {adapter_class.__name__}")
        
        # 测试紧急回滚
        SimulationConfig.emergency_fallback()
        PVModelAdapter._instance = None
        adapter_class = get_adapter_class()
        print(f"   ✅ 紧急回滚: {adapter_class.__name__}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 切换功能错误: {e}")
        return False

def verify_django_integration():
    """验证Django集成"""
    print("\n🌐 验证Django集成...")
    
    try:
        # 检查Django设置
        from django.conf import settings
        
        if hasattr(settings, 'USE_REAL_SIMULATION'):
            print("   ✅ Django设置已配置")
        else:
            print("   ❌ Django设置缺失")
            return False
        
        if hasattr(settings, 'SIMULATION_CONFIG'):
            print("   ✅ 仿真配置已添加")
        else:
            print("   ❌ 仿真配置缺失")
            return False
        
        # 检查路径配置
        if str(settings.BASE_DIR.parent / 'src') in sys.path:
            print("   ✅ src路径已添加到sys.path")
        else:
            print("   ⚠️ src路径可能未正确添加")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Django集成错误: {e}")
        return False

def main():
    """主验证函数"""
    print("🔍 阶段一重构最终验证")
    print("=" * 50)
    
    verifications = [
        ("文件创建", verify_files_created),
        ("模块导入", verify_imports),
        ("配置功能", verify_configuration),
        ("切换功能", verify_switching),
        ("Django集成", verify_django_integration),
    ]
    
    passed = 0
    total = len(verifications)
    
    for name, verify_func in verifications:
        try:
            if verify_func():
                passed += 1
                print(f"\n✅ {name} 验证通过")
            else:
                print(f"\n❌ {name} 验证失败")
        except Exception as e:
            print(f"\n❌ {name} 验证异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 阶段一重构完全成功！")
        print("✅ 所有核心功能都已正确实现")
        print("🚀 系统已准备好进入下一阶段")
        
        print("\n📋 完成的功能:")
        print("   • 配置管理和开关机制")
        print("   • 适配器工厂模式")
        print("   • 数据格式转换")
        print("   • 天气数据桥接")
        print("   • Django集成")
        print("   • 紧急回滚机制")
        
        print("\n🎯 使用方法:")
        print("   # 使用模拟系统（默认）")
        print("   python manage.py runserver")
        print("")
        print("   # 使用真实仿真系统")
        print("   set USE_REAL_SIMULATION=true")
        print("   python manage.py runserver")
        
    elif passed >= total * 0.8:
        print("\n✅ 阶段一重构基本成功！")
        print("⚠️ 部分功能可能需要微调")
    else:
        print("\n❌ 阶段一重构需要进一步完善")
    
    return passed >= total * 0.8

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
