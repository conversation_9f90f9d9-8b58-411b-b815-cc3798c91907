#!/usr/bin/env python
"""
测试仿真系统切换功能
"""

import os
import sys
from pathlib import Path

# 添加Django项目路径
project_root = Path(__file__).resolve().parent
django_path = project_root / 'pv_digital_twin'
sys.path.insert(0, str(django_path))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pv_digital_twin.settings')

import django
django.setup()

def test_simulation_switch():
    """测试仿真系统切换功能"""
    print("=" * 60)
    print("测试仿真系统切换功能")
    print("=" * 60)
    
    from dashboard.config import SimulationConfig, get_adapter_class
    from dashboard.pv_model_adapter import PVModelAdapter
    
    # 测试1: 默认使用模拟系统
    print("\n1. 测试默认模拟系统")
    print("-" * 30)
    
    # 确保环境变量未设置
    os.environ.pop('USE_REAL_SIMULATION', None)
    
    # 重置单例实例
    PVModelAdapter._instance = None
    
    use_real = SimulationConfig.use_real_simulation()
    adapter_class = get_adapter_class()
    
    print(f"使用真实仿真: {use_real}")
    print(f"适配器类: {adapter_class.__name__}")
    
    if not use_real and adapter_class.__name__ == 'PVModelAdapter':
        print("✅ 默认模拟系统测试通过")
    else:
        print("❌ 默认模拟系统测试失败")
        return False
    
    # 测试2: 切换到真实系统
    print("\n2. 测试切换到真实系统")
    print("-" * 30)
    
    # 设置环境变量启用真实仿真
    os.environ['USE_REAL_SIMULATION'] = 'true'
    
    # 重置单例实例
    PVModelAdapter._instance = None
    
    use_real = SimulationConfig.use_real_simulation()
    adapter_class = get_adapter_class()
    
    print(f"使用真实仿真: {use_real}")
    print(f"适配器类: {adapter_class.__name__}")
    
    if use_real:
        print("✅ 真实系统配置检测通过")
        
        # 由于src模块可能不可用，适配器可能会回滚到模拟系统
        if adapter_class.__name__ == 'RealPVModelAdapter':
            print("✅ 真实适配器类获取成功")
        else:
            print("⚠️ 真实适配器不可用，已回滚到模拟系统（这是正常的）")
    else:
        print("❌ 真实系统配置失败")
        return False
    
    # 测试3: 紧急回滚功能
    print("\n3. 测试紧急回滚功能")
    print("-" * 30)
    
    # 执行紧急回滚
    SimulationConfig.emergency_fallback()
    
    # 重置单例实例
    PVModelAdapter._instance = None
    
    use_real = SimulationConfig.use_real_simulation()
    adapter_class = get_adapter_class()
    
    print(f"回滚后使用真实仿真: {use_real}")
    print(f"回滚后适配器类: {adapter_class.__name__}")
    
    if not use_real and adapter_class.__name__ == 'PVModelAdapter':
        print("✅ 紧急回滚功能测试通过")
    else:
        print("❌ 紧急回滚功能测试失败")
        return False
    
    # 测试4: 配置验证功能
    print("\n4. 测试配置验证功能")
    print("-" * 30)
    
    success, message = SimulationConfig.validate_src_modules()
    print(f"src模块验证: {success}")
    print(f"验证消息: {message}")
    
    # 无论验证是否成功，这都是正常的
    print("✅ 配置验证功能测试通过")
    
    # 测试5: 路径配置功能
    print("\n5. 测试路径配置功能")
    print("-" * 30)
    
    src_path = SimulationConfig.get_src_path()
    model_path = SimulationConfig.get_model_path()
    
    print(f"src路径: {src_path}")
    print(f"模型路径: {model_path}")
    
    if src_path and model_path:
        print("✅ 路径配置功能测试通过")
    else:
        print("❌ 路径配置功能测试失败")
        return False
    
    return True

def test_adapter_compatibility():
    """测试适配器兼容性"""
    print("\n" + "=" * 60)
    print("测试适配器兼容性")
    print("=" * 60)
    
    from dashboard.pv_model_adapter import PVModelAdapter
    
    # 确保使用模拟系统
    os.environ.pop('USE_REAL_SIMULATION', None)
    PVModelAdapter._instance = None
    
    try:
        # 获取适配器实例
        adapter = PVModelAdapter.get_instance()
        print(f"适配器类型: {type(adapter).__name__}")
        
        # 测试所有必要的方法
        methods_to_test = [
            'get_simulation_data',
            'get_system_info',
            'get_detected_anomalies',
            'get_daily_energy',
            'get_simulation_logs',
        ]
        
        for method_name in methods_to_test:
            if hasattr(adapter, method_name):
                method = getattr(adapter, method_name)
                result = method()
                print(f"✅ {method_name}: {type(result).__name__}")
            else:
                print(f"❌ {method_name}: 方法不存在")
                return False
        
        print("✅ 适配器兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 适配器兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始仿真系统切换测试")
    
    tests = [
        ("仿真系统切换", test_simulation_switch),
        ("适配器兼容性", test_adapter_compatibility),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n🎉 {test_name} 测试通过")
            else:
                print(f"\n❌ {test_name} 测试失败")
        except Exception as e:
            print(f"\n❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有切换功能测试通过！")
        print("✅ 阶段一重构的配置开关功能正常工作！")
    else:
        print("⚠️ 部分切换功能测试失败")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
