#!/usr/bin/env python
"""
简单的阶段一重构测试脚本
"""

import os
import sys
from pathlib import Path

# 添加Django项目路径
project_root = Path(__file__).resolve().parent
django_path = project_root / 'pv_digital_twin'
sys.path.insert(0, str(django_path))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pv_digital_twin.settings')

import django
django.setup()

def test_basic_imports():
    """测试基本导入"""
    print("测试基本导入...")
    
    try:
        # 测试配置模块
        from dashboard.config import SimulationConfig
        print("✅ 配置模块导入成功")
        
        # 测试数据转换器
        from dashboard.data_format_converter import DataFormatConverter
        print("✅ 数据格式转换器导入成功")
        
        # 测试天气桥接器
        from dashboard.weather_data_bridge import WeatherDataBridge
        print("✅ 天气数据桥接器导入成功")
        
        # 测试PV适配器
        from dashboard.pv_model_adapter import PVModelAdapter
        print("✅ PV模型适配器导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_configuration():
    """测试配置功能"""
    print("\n测试配置功能...")
    
    try:
        from dashboard.config import SimulationConfig
        
        # 测试配置读取
        use_real = SimulationConfig.use_real_simulation()
        print(f"使用真实仿真: {use_real}")
        
        # 测试路径获取
        src_path = SimulationConfig.get_src_path()
        print(f"src路径: {src_path}")
        
        # 测试模块验证
        success, message = SimulationConfig.validate_src_modules()
        print(f"src模块验证: {success} - {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_adapter_creation():
    """测试适配器创建"""
    print("\n测试适配器创建...")
    
    try:
        from dashboard.pv_model_adapter import PVModelAdapter
        
        # 获取适配器实例
        adapter = PVModelAdapter.get_instance()
        print(f"适配器类型: {type(adapter).__name__}")
        
        # 测试基本方法
        system_info = adapter.get_system_info()
        print(f"系统信息获取成功: {len(system_info)} 个字段")
        
        return True
        
    except Exception as e:
        print(f"❌ 适配器创建失败: {e}")
        return False

def test_data_conversion():
    """测试数据转换"""
    print("\n测试数据转换...")
    
    try:
        from dashboard.data_format_converter import DataFormatConverter
        import pandas as pd
        from datetime import datetime
        
        # 创建测试数据
        test_data = {
            'datetime': [datetime.now()],
            'ac_power': [1000],
            'dc_power': [1100],
            'temp_air': [25],
            'ghi': [800]
        }
        
        # 测试转换
        result = DataFormatConverter.pvlib_to_django_format(test_data)
        print(f"数据转换成功: {len(result)} 个字段")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据转换失败: {e}")
        return False

def test_django_startup():
    """测试Django启动"""
    print("\n测试Django启动...")
    
    try:
        from django.core.management import execute_from_command_line
        
        # 测试Django检查
        os.system('cd pv_digital_twin && python manage.py check --deploy')
        print("✅ Django检查通过")
        
        return True
        
    except Exception as e:
        print(f"❌ Django启动测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("阶段一重构简单测试")
    print("=" * 60)
    
    tests = [
        ("基本导入", test_basic_imports),
        ("配置功能", test_configuration),
        ("适配器创建", test_adapter_creation),
        ("数据转换", test_data_conversion),
        ("Django启动", test_django_startup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！阶段一重构基本成功！")
    elif passed >= total * 0.8:
        print("✅ 大部分测试通过，重构基本成功！")
    else:
        print("⚠️ 多个测试失败，需要进一步检查")
    
    return passed >= total * 0.8

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
