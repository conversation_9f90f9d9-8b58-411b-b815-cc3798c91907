#!/usr/bin/env python
"""
阶段一重构功能演示脚本

展示配置开关、适配器切换等核心功能。
"""

import os
import sys
import time
from pathlib import Path

# 添加Django项目路径
project_root = Path(__file__).resolve().parent
django_path = project_root / 'pv_digital_twin'
sys.path.insert(0, str(django_path))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pv_digital_twin.settings')

import django
django.setup()

def demo_configuration_management():
    """演示配置管理功能"""
    print("🔧 配置管理功能演示")
    print("=" * 50)
    
    from dashboard.config import SimulationConfig
    
    # 显示当前配置
    print("📋 当前配置状态:")
    print(f"  - 使用真实仿真: {SimulationConfig.use_real_simulation()}")
    print(f"  - src路径: {SimulationConfig.get_src_path()}")
    print(f"  - 模型路径: {SimulationConfig.get_model_path()}")
    
    # 显示配置详情
    config = SimulationConfig.get_simulation_config()
    print(f"  - 缓存超时: {config['CACHE_TIMEOUT']}秒")
    print(f"  - 最大仿真点数: {config['MAX_SIMULATION_POINTS']}")
    print(f"  - 响应时间限制: {config['RESPONSE_TIME_LIMIT']}秒")
    
    # 验证src模块
    success, message = SimulationConfig.validate_src_modules()
    print(f"  - src模块状态: {'✅ 可用' if success else '❌ 不可用'}")
    print(f"  - 验证消息: {message}")

def demo_adapter_switching():
    """演示适配器切换功能"""
    print("\n🔄 适配器切换功能演示")
    print("=" * 50)
    
    from dashboard.config import get_adapter_class
    from dashboard.pv_model_adapter import PVModelAdapter
    
    # 演示1: 默认模拟系统
    print("1️⃣ 默认模拟系统")
    os.environ.pop('USE_REAL_SIMULATION', None)
    PVModelAdapter._instance = None
    
    adapter_class = get_adapter_class()
    print(f"   适配器类: {adapter_class.__name__}")
    
    # 演示2: 切换到真实系统
    print("\n2️⃣ 切换到真实系统")
    os.environ['USE_REAL_SIMULATION'] = 'true'
    PVModelAdapter._instance = None
    
    adapter_class = get_adapter_class()
    print(f"   适配器类: {adapter_class.__name__}")
    
    # 演示3: 紧急回滚
    print("\n3️⃣ 紧急回滚演示")
    from dashboard.config import SimulationConfig
    SimulationConfig.emergency_fallback()
    PVModelAdapter._instance = None
    
    adapter_class = get_adapter_class()
    print(f"   回滚后适配器类: {adapter_class.__name__}")

def demo_data_conversion():
    """演示数据转换功能"""
    print("\n📊 数据转换功能演示")
    print("=" * 50)
    
    from dashboard.data_format_converter import DataFormatConverter
    import pandas as pd
    from datetime import datetime, timedelta
    
    # 创建示例数据
    print("📝 创建示例pvlib数据...")
    sample_data = pd.DataFrame({
        'datetime': [datetime.now() - timedelta(hours=i) for i in range(3, 0, -1)],
        'ac_power': [1200, 1500, 1800],
        'dc_power': [1300, 1600, 1900],
        'temp_air': [22, 25, 28],
        'temp_cell': [25, 30, 35],
        'ghi': [600, 800, 900]
    })
    
    print(f"   原始数据: {len(sample_data)} 行")
    print(f"   列名: {list(sample_data.columns)}")
    
    # 转换数据
    print("\n🔄 转换为Django API格式...")
    converted_data = DataFormatConverter.pvlib_to_django_format(sample_data)
    
    print(f"   转换后字段: {list(converted_data.keys())}")
    print(f"   时间戳数量: {len(converted_data['timestamps'])}")
    print(f"   功率数据点: {len(converted_data['ac_power'])}")
    
    # 显示部分数据
    if converted_data['timestamps']:
        print(f"   最新时间戳: {converted_data['timestamps'][-1]}")
        print(f"   最新AC功率: {converted_data['ac_power'][-1]}W")
        print(f"   最新效率: {converted_data['efficiency'][-1]:.2f}%")

def demo_weather_bridge():
    """演示天气数据桥接功能"""
    print("\n🌤️ 天气数据桥接功能演示")
    print("=" * 50)
    
    from dashboard.weather_data_bridge import WeatherDataBridge
    from datetime import datetime, timedelta
    
    # 创建天气桥接器
    print("🌍 初始化天气数据桥接器...")
    bridge = WeatherDataBridge()
    
    # 生成天气数据
    print("📡 生成天气数据...")
    start_time = datetime.now()
    end_time = start_time + timedelta(hours=6)
    
    weather_data = bridge.generate_weather_data(
        start_time=start_time,
        end_time=end_time,
        freq='H',
        latitude=39.9,
        longitude=116.4
    )
    
    print(f"   生成数据点: {len(weather_data)}")
    print(f"   数据列: {list(weather_data.columns)}")
    
    if not weather_data.empty:
        print(f"   平均GHI: {weather_data['ghi'].mean():.2f} W/m²")
        print(f"   平均温度: {weather_data['temp_air'].mean():.2f} °C")
        print(f"   平均风速: {weather_data['wind_speed'].mean():.2f} m/s")
    
    # 验证数据
    is_valid = bridge.validate_weather_data(weather_data)
    print(f"   数据验证: {'✅ 通过' if is_valid else '❌ 失败'}")

def demo_adapter_functionality():
    """演示适配器功能"""
    print("\n⚙️ 适配器功能演示")
    print("=" * 50)
    
    from dashboard.pv_model_adapter import PVModelAdapter
    
    # 确保使用模拟系统
    os.environ.pop('USE_REAL_SIMULATION', None)
    PVModelAdapter._instance = None
    
    print("🔧 获取适配器实例...")
    adapter = PVModelAdapter.get_instance()
    print(f"   适配器类型: {type(adapter).__name__}")
    
    # 等待初始化完成
    print("⏳ 等待仿真初始化...")
    time.sleep(2)
    
    # 获取系统信息
    print("\n📊 系统信息:")
    system_info = adapter.get_system_info()
    for key, value in system_info.items():
        if isinstance(value, float):
            print(f"   {key}: {value:.2f}")
        else:
            print(f"   {key}: {value}")
    
    # 获取仿真数据
    print("\n📈 仿真数据:")
    sim_data = adapter.get_simulation_data()
    for key, values in sim_data.items():
        if values and isinstance(values, list):
            print(f"   {key}: {len(values)} 个数据点")
    
    # 获取日志
    logs = adapter.get_simulation_logs()
    print(f"\n📝 仿真日志: {len(logs)} 条记录")
    if logs:
        print(f"   最新日志: {logs[-1]['message'][:50]}...")

def main():
    """主演示函数"""
    print("🎉 阶段一重构功能演示")
    print("=" * 60)
    print("本演示将展示阶段一重构完成的核心功能：")
    print("- 配置管理")
    print("- 适配器切换")
    print("- 数据转换")
    print("- 天气数据桥接")
    print("- 适配器功能")
    print("=" * 60)
    
    try:
        demo_configuration_management()
        demo_adapter_switching()
        demo_data_conversion()
        demo_weather_bridge()
        demo_adapter_functionality()
        
        print("\n" + "=" * 60)
        print("🎊 演示完成！")
        print("✅ 阶段一重构的所有核心功能都正常工作")
        print("🚀 系统已准备好进入阶段二的深度集成")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
